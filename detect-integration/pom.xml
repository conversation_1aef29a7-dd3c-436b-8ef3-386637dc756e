<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dcai.detect</groupId>
        <artifactId>detect</artifactId>
        <version>0.0.1</version>
    </parent>

    <artifactId>detect-integration</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.dcai.detect</groupId>
            <artifactId>detect-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ejuetc.commons</groupId>
            <artifactId>commons-base</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
    </dependencies>

</project>
