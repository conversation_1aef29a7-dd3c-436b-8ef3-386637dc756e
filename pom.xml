<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ejuetc.commons</groupId>
        <artifactId>commons</artifactId>
        <version>0.6.1</version>
    </parent>

    <groupId>com.dcai.detect</groupId>
    <artifactId>detect</artifactId>
    <packaging>pom</packaging>
    <version>0.0.1</version>
    <modules>
        <module>detect-sdk</module>
        <module>detect-api</module>
        <module>detect-application</module>
        <module>detect-domain</module>
        <module>detect-integration</module>
    </modules>

    <properties>
        <commons.version>0.6.1</commons.version>
        <message.version>0.1.1</message.version>
        <swagger.version>2.2.0</swagger.version>
        <spring-cloud-alibaba.version>2021.0.4.0</spring-cloud-alibaba.version>
        <validation-api.version>2.0.1.Final</validation-api.version>
    </properties>
    
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.dcai.message</groupId>
                <artifactId>message-api</artifactId>
                <version>${message.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ejuetc.commons</groupId>
                <artifactId>commons-base</artifactId>
                <version>${commons.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ejuetc.commons</groupId>
                <artifactId>commons-querydomain</artifactId>
                <version>${commons.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation-api.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.ejuetc.commons</groupId>
            <artifactId>commons-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ejuetc.commons</groupId>
            <artifactId>commons-querydomain</artifactId>
        </dependency>
    </dependencies>

</project>
