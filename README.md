# 检测模块 (Detect Module)

基于领域驱动设计(DDD)的物业检测管理系统，支持多机构协作的检测业务流程。

## 项目背景

### 合作背景
本系统与**中国房地产业协会（中国房协）**建立紧密合作关系，旨在构建标准化的小区（可泛化到其他场所）检测标准体系，并基于该检测标准为物业公司提供评级服务，进而驱动检测标准的实施和检测结果的应用。

### 业务目标
1. **标准化检测体系** - 建立统一的检测标准和流程规范
2. **物业公司评级** - 基于检测结果对物业公司进行等级评定
3. **检测机构认证** - 建立检测机构培训、考核、资质认证体系
4. **检测结果应用** - 推动检测标准实施，提升物业管理水平

### 检测标准分层体系

#### 1. 日常检测
- **数据来源**：物业公司日常维护数据
- **录入方式**：系统对接 + 手工录入
- **检测频率**：持续性
- **主要目的**：日常维护记录，基础数据积累

#### 2. 基础检测
- **检测频率**：根据小区使用年限动态调整
  - **6年内小区**：3年一检
  - **10年内小区**：2年一检
  - **10年外小区**：每年一检
- **执行主体**：具备相应资质的检测机构
- **主要目的**：定期全面检测，发现潜在问题

#### 3. 专项检测
- **触发条件**：基础检测发现重大异常
- **执行主体**：专业检测机构
- **检测范围**：特定部位深入检测
- **主要目的**：深度排查，制定整改方案

### 检测机构资质认证体系

#### 培训体系
- **培训机构**：中国房协指定培训机构
- **培训内容**：检测标准、操作规范、质量控制等
- **培训等级**：基础级、中级、高级、专家级
- **培训类型**：水质、空气质量、噪音、结构、电气、消防等专项培训

#### 考核认证
- **考核标准**：中国房协统一考核标准
- **考核方式**：理论考试 + 实操考核
- **证书颁发**：考核通过后颁发资质证书
- **证书管理**：证书有效期管理、到期续证

#### 资质等级
- **特级**：综合检测能力，可承担所有类型检测
- **甲级**：专业检测能力强，可承担大部分检测项目
- **乙级**：具备基本检测能力，可承担常规检测项目
- **丙级**：初级检测能力，可承担简单检测项目

## 项目架构

### 模块结构
```
detect/
├── detect-api/          # API接口定义和DTO
├── detect-domain/       # 领域模型和业务逻辑
├── detect-application/  # 应用层和启动类
├── detect-integration/  # 外部集成
└── detect-sdk/         # SDK工具
```

### 技术栈
- **框架**: Spring Boot 3.x + Spring Cloud
- **数据库**: MySQL + Redis
- **ORM**: JPA/Hibernate
- **服务发现**: Nacos
- **API文档**: Swagger/OpenAPI 3
- **构建工具**: Maven

## 领域模型

### 核心实体分层

#### 系统级实体（蓝色 - 系统能力配置）

##### 1. 检测套餐 (Detect)
- 检测类型或检测套餐定义
- 用于物业公司选择检测类型
- 系统预配置的检测能力

##### 2. 检测项 (Item)
- 标准化的检测指标定义
- 配置小区可检测的指标
- 定义指标的可选值和数据生成方式

##### 3. 检测套餐项关联 (DetectItem)
- Detect与Item的多对多关联体
- 管理检测套餐包含的检测项
- 配置检测项在套餐中的参数

##### 4. 资质管理 (Quality)
- 定义检测套餐所需的机构资质
- 约束具备相应资质的机构才能执行特定检测

#### 用户级实体（黄色 - 用户及配置）

##### 5. 机构管理 (Organ)
- 所有客户机构的基础类型
- 包括物业公司、检测机构、行业协会等
- 可拥有多个操作人员(Operator)

##### 6. 物业场所 (PropertyPlace)
- 检测的目标对象
- 与物业公司具有一对多关系
- 通过检测活动了解各项指标状况

#### 行为级实体（红色 - 用户交易活动）

##### 7. 检测项目 (Project)
- 由物业公司发起的检测项目
- 选定检测套餐(Detect)和检测场所
- 项目全生命周期管理

##### 8. 检测表单 (Form)
- 数据收集表单（GatherForm）
- 结果报告表单（ReportForm）
- 将检测指标项值汇总成特定格式

##### 9. 项目值 (ItemValue)
- 记录特定指标项针对特定物业的检测结果
- 检测活动的核心数据载体

### 实体关系说明

#### 系统级实体关系
- **Detect** ↔ **DetectItem** ↔ **Item**: 检测套餐通过DetectItem关联多个检测项，形成多对多关系
- **Quality**: 独立的资质管理，可与检测套餐关联（通过requireQuality标识）

#### 用户级实体关系
- **Organ** → **PropertyPlace**: 物业公司（Organ的一种类型）管理多个物业场所

#### 行为级实体关系
- **Project** 核心关联：
  - 选择一个检测套餐（Detect）
  - 指定一个物业场所（PropertyPlace）
  - 涉及三个机构：委托机构、检测机构、监管机构（都是Organ）
- **Form** → **Project**: 每个项目可以有多个表单（数据收集表单和结果报告表单）
- **ItemValue** 多重关联：
  - 属于一个项目（Project）
  - 对应一个检测项（Item）
  - 针对一个物业场所（PropertyPlace）

### 业务流程

#### 1. 系统配置阶段
1. 配置检测项（Item）- 定义可检测的指标
2. 配置检测套餐（Detect）- 定义检测类型
3. 配置套餐项关联（DetectItem）- 定义套餐包含哪些检测项
4. 配置资质要求（Quality）- 定义检测套餐的资质要求

#### 2. 用户准备阶段
1. 注册机构（Organ）- 物业公司、检测机构等
2. 登记物业场所（PropertyPlace）- 被检测的目标场所

#### 3. 检测执行阶段
1. 创建检测项目（Project）- 物业公司发起，选择套餐和场所
2. 生成检测表单（Form）- 数据收集表单和报告表单
3. 记录检测数据（ItemValue）- 针对每个检测项记录实际检测值
4. 完成项目流程 - 数据审核、报告生成

#### 5. 表单系统 (Form)
- **采集表单**: 现场数据采集
- **报告表单**: 检测结果报告
- 支持动态表单配置

#### 6. 设备管理 (Device)
- **采集设备**: 数据采集工具
- **分析器**: 数据分析处理
- **生成器**: 报告生成工具

## 业务流程

### 1. 项目创建流程
1. 物业公司创建检测项目
2. 指定检测机构和监管机构
3. 设置检测计划和要求
4. 提交审批

### 2. 检测执行流程
1. 项目审批通过后开始执行
2. 检测机构安排检测活动
3. 使用设备采集数据
4. 填写采集表单
5. 数据分析处理
6. 生成检测报告

### 3. 质量监管流程
1. 监管机构监督检测过程
2. 审核检测结果
3. 质量评估和反馈

## 快速开始

### 环境要求
- JDK 17+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 启动步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd detect
```

2. **配置数据库**
```yaml
# application-local.yaml
spring:
  datasource:
    url: ***************************************
    username: your_username
    password: your_password
```

3. **启动应用**
```bash
mvn clean install
cd detect-application
mvn spring-boot:run
```

4. **访问接口文档**
```
http://localhost:8022/detect/swagger-ui.html
```

## API接口

### 机构管理
- `POST /api/organ/create` - 创建机构
- `GET /api/organ/list` - 查询机构列表
- `PUT /api/organ/update/{id}` - 更新机构信息

### 项目管理
- `POST /api/project/create` - 创建项目
- `GET /api/project/list` - 查询项目列表
- `POST /api/project/approve/{id}` - 审批项目

### 检测管理
- `POST /api/detect/create` - 创建检测活动
- `GET /api/detect/list` - 查询检测列表
- `POST /api/detect/complete/{id}` - 完成检测

## 配置说明

### 数据库配置
```yaml
spring:
  jpa:
    hibernate:
      ddl-auto: update  # 开发环境使用，生产环境改为validate
    show-sql: true      # 开发环境显示SQL
```

### 缓存配置
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      timeout: 5000
```

### 服务发现配置
```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: dcai
```

## 开发指南

### 代码规范
1. 遵循DDD设计原则
2. 实体类继承BaseEntity
3. 使用Repository模式访问数据
4. API接口使用统一响应格式

### 数据库设计
- 表名使用`tb_`前缀
- 字段使用下划线命名
- 必须包含逻辑删除字段
- 添加适当的索引和约束

### 测试
```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn integration-test
```

## 部署说明

### Docker部署
```bash
# 构建镜像
docker build -t detect-app .

# 运行容器
docker run -p 8022:8022 detect-app
```

### 生产环境配置
- 修改数据库连接配置
- 调整JVM参数
- 配置日志输出
- 设置监控告警

## 贡献指南

1. Fork项目
2. 创建特性分支
3. 提交代码
4. 创建Pull Request

## 许可证

MIT License
