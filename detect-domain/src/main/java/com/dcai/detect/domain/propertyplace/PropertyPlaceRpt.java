package com.dcai.detect.domain.propertyplace;

import com.dcai.detect.dto.PropertyPlaceDTO;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface PropertyPlaceRpt extends JpaRepositoryImplementation<PropertyPlace, Long> {

    @Cacheable("PropertyPlaceRpt.findByCode")
    Optional<PropertyPlace> findByCode(String code);

    List<PropertyPlace> findByPropertyCompanyId(Long propertyCompanyId);

    List<PropertyPlace> findByType(PropertyPlaceDTO.PlaceType type);

    List<PropertyPlace> findByStatus(PropertyPlaceDTO.PlaceStatus status);

    List<PropertyPlace> findByPropertyCompanyIdAndStatus(Long propertyCompanyId, PropertyPlaceDTO.PlaceStatus status);

    @Query("SELECT pp FROM PropertyPlace pp WHERE " +
           "(:name IS NULL OR pp.name LIKE %:name%) AND " +
           "(:type IS NULL OR pp.type = :type) AND " +
           "(:propertyCompanyId IS NULL OR pp.propertyCompanyId = :propertyCompanyId) AND " +
           "(:status IS NULL OR pp.status = :status)")
    Page<PropertyPlace> findByConditions(@Param("name") String name,
                                        @Param("type") PropertyPlaceDTO.PlaceType type,
                                        @Param("propertyCompanyId") Long propertyCompanyId,
                                        @Param("status") PropertyPlaceDTO.PlaceStatus status,
                                        Pageable pageable);

    @Query("SELECT COUNT(pp) FROM PropertyPlace pp WHERE pp.propertyCompanyId = :propertyCompanyId AND pp.status = :status")
    long countByPropertyCompanyIdAndStatus(@Param("propertyCompanyId") Long propertyCompanyId, 
                                          @Param("status") PropertyPlaceDTO.PlaceStatus status);

    @Query("SELECT COUNT(pp) FROM PropertyPlace pp WHERE pp.status = :status")
    long countByStatus(@Param("status") PropertyPlaceDTO.PlaceStatus status);
}
