package com.dcai.detect.domain.detectitem;

import com.ejuetc.commons.base.entity.BaseEntity;
import com.dcai.detect.dto.DetectItemDTO;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Entity
@Slf4j
@NoArgsConstructor
@Comment("检测套餐项关联")
@Table(name = "tb_detect_item", 
       uniqueConstraints = @UniqueConstraint(name = "uk_detect_item", columnNames = {"detect_id", "item_id"}),
       indexes = {
           @Index(name = "idx_detect_item_detect_id", columnList = "detect_id"),
           @Index(name = "idx_detect_item_item_id", columnList = "item_id")
       })
@Where(clause = "logic_delete = 0")
public class DetectItem extends BaseEntity<DetectItem> implements Serializable {

    @Id
    @Comment("关联主键")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "detect_id", columnDefinition = "bigint(20) COMMENT '检测套餐ID'", nullable = false)
    private Long detectId;

    @Column(name = "item_id", columnDefinition = "bigint(20) COMMENT '检测项ID'", nullable = false)
    private Long itemId;

    @Column(name = "required", columnDefinition = "tinyint(1) COMMENT '是否必检'", nullable = false)
    private Boolean required;

    @Column(name = "sort_order", columnDefinition = "int COMMENT '检测顺序'")
    private Integer sortOrder;

    @Column(name = "item_price", columnDefinition = "decimal(10,2) COMMENT '单项价格'")
    private BigDecimal itemPrice;

    @Column(name = "estimated_minutes", columnDefinition = "int COMMENT '预计检测时长(分钟)'")
    private Integer estimatedMinutes;

    @Column(name = "special_requirement", columnDefinition = "text COMMENT '特殊要求'")
    private String specialRequirement;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", columnDefinition = "varchar(31) COMMENT '关联状态'", nullable = false)
    private DetectItemDTO.DetectItemStatus status;

    @Column(name = "remark", columnDefinition = "text COMMENT '备注'")
    private String remark;

    public DetectItem(Long detectId, Long itemId, Boolean required, Integer sortOrder,
                     BigDecimal itemPrice, Integer estimatedMinutes, String specialRequirement, String remark) {
        this.detectId = detectId;
        this.itemId = itemId;
        this.required = required;
        this.sortOrder = sortOrder;
        this.itemPrice = itemPrice;
        this.estimatedMinutes = estimatedMinutes;
        this.specialRequirement = specialRequirement;
        this.remark = remark;
        this.status = DetectItemDTO.DetectItemStatus.ACTIVE;
    }

    public void updateConfig(Boolean required, Integer sortOrder, BigDecimal itemPrice,
                           Integer estimatedMinutes, String specialRequirement, String remark) {
        this.required = required;
        this.sortOrder = sortOrder;
        this.itemPrice = itemPrice;
        this.estimatedMinutes = estimatedMinutes;
        this.specialRequirement = specialRequirement;
        this.remark = remark;
    }

    public void activate() {
        this.status = DetectItemDTO.DetectItemStatus.ACTIVE;
    }

    public void deactivate() {
        this.status = DetectItemDTO.DetectItemStatus.INACTIVE;
    }

    public boolean isActive() {
        return DetectItemDTO.DetectItemStatus.ACTIVE.equals(this.status);
    }

    public boolean isRequired() {
        return Boolean.TRUE.equals(this.required);
    }
}
