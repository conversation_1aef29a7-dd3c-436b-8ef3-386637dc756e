package com.dcai.detect.domain.detectitem;

import com.dcai.detect.dto.DetectItemDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface DetectItemRpt extends JpaRepositoryImplementation<DetectItem, Long> {

    Optional<DetectItem> findByDetectIdAndItemId(Long detectId, Long itemId);

    List<DetectItem> findByDetectIdAndStatus(Long detectId, DetectItemDTO.DetectItemStatus status);

    List<DetectItem> findByItemIdAndStatus(Long itemId, DetectItemDTO.DetectItemStatus status);

    List<DetectItem> findByDetectIdAndRequired(Long detectId, Boolean required);

    @Query("SELECT di FROM DetectItem di WHERE di.detectId = :detectId AND di.status = :status ORDER BY di.sortOrder ASC")
    List<DetectItem> findByDetectIdOrderBySortOrder(@Param("detectId") Long detectId, 
                                                   @Param("status") DetectItemDTO.DetectItemStatus status);

    @Query("SELECT di FROM DetectItem di WHERE " +
           "(:detectId IS NULL OR di.detectId = :detectId) AND " +
           "(:itemId IS NULL OR di.itemId = :itemId) AND " +
           "(:required IS NULL OR di.required = :required) AND " +
           "(:status IS NULL OR di.status = :status)")
    Page<DetectItem> findByConditions(@Param("detectId") Long detectId,
                                     @Param("itemId") Long itemId,
                                     @Param("required") Boolean required,
                                     @Param("status") DetectItemDTO.DetectItemStatus status,
                                     Pageable pageable);

    @Query("SELECT COUNT(di) FROM DetectItem di WHERE di.detectId = :detectId AND di.status = :status")
    long countByDetectIdAndStatus(@Param("detectId") Long detectId, @Param("status") DetectItemDTO.DetectItemStatus status);

    @Query("SELECT COUNT(di) FROM DetectItem di WHERE di.detectId = :detectId AND di.required = true AND di.status = :status")
    long countRequiredByDetectId(@Param("detectId") Long detectId, @Param("status") DetectItemDTO.DetectItemStatus status);
}
