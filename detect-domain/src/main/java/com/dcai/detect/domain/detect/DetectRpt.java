package com.dcai.detect.domain.detect;

import com.dcai.detect.dto.DetectDTO;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface DetectRpt extends JpaRepositoryImplementation<Detect, Long> {

    @Cacheable("DetectRpt.findByCode")
    Optional<Detect> findByCode(String code);

    List<Detect> findByType(DetectDTO.DetectType type);

    List<Detect> findByStatus(DetectDTO.DetectStatus status);

    List<Detect> findByRequireQuality(Boolean requireQuality);

    @Query("SELECT d FROM Detect d WHERE " +
           "(:name IS NULL OR d.name LIKE %:name%) AND " +
           "(:type IS NULL OR d.type = :type) AND " +
           "(:status IS NULL OR d.status = :status) AND " +
           "(:requireQuality IS NULL OR d.requireQuality = :requireQuality)")
    Page<Detect> findByConditions(@Param("name") String name,
                                 @Param("type") DetectDTO.DetectType type,
                                 @Param("status") DetectDTO.DetectStatus status,
                                 @Param("requireQuality") Boolean requireQuality,
                                 Pageable pageable);

    @Query("SELECT COUNT(d) FROM Detect d WHERE d.status = :status")
    long countByStatus(@Param("status") DetectDTO.DetectStatus status);
}
