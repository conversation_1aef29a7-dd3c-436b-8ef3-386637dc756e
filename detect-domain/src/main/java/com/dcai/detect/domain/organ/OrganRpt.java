package com.dcai.detect.domain.organ;

import com.dcai.detect.dto.OrganDTO;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface OrganRpt extends JpaRepositoryImplementation<Organ, Long> {

    @Cacheable("OrganRpt.findByCode")
    Optional<Organ> findByCode(String code);

    List<Organ> findByType(OrganDTO.OrganType type);

    List<Organ> findByStatus(OrganDTO.OrganStatus status);

    @Query("SELECT o FROM Organ o WHERE " +
           "(:name IS NULL OR o.name LIKE %:name%) AND " +
           "(:type IS NULL OR o.type = :type) AND " +
           "(:status IS NULL OR o.status = :status)")
    Page<Organ> findByConditions(@Param("name") String name,
                                @Param("type") OrganDTO.OrganType type,
                                @Param("status") OrganDTO.OrganStatus status,
                                Pageable pageable);

    @Query("SELECT COUNT(o) FROM Organ o WHERE o.type = :type AND o.status = :status")
    long countByTypeAndStatus(@Param("type") OrganDTO.OrganType type, 
                             @Param("status") OrganDTO.OrganStatus status);
}
