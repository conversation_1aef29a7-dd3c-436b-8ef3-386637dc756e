package com.dcai.detect.domain.itemvalue;

import com.dcai.detect.dto.ItemValueDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface ItemValueRpt extends JpaRepositoryImplementation<ItemValue, Long> {

    Optional<ItemValue> findByProjectIdAndItemIdAndPropertyPlaceIdAndLocation(
            Long projectId, Long itemId, Long propertyPlaceId, String location);

    List<ItemValue> findByProjectId(Long projectId);

    List<ItemValue> findByProjectIdAndStatus(Long projectId, ItemValueDTO.ValueStatus status);

    List<ItemValue> findByItemId(Long itemId);

    List<ItemValue> findByPropertyPlaceId(Long propertyPlaceId);

    List<ItemValue> findByResult(ItemValueDTO.DetectResult result);

    List<ItemValue> findByDetector(String detector);

    @Query("SELECT iv FROM ItemValue iv WHERE iv.projectId = :projectId AND iv.itemId = :itemId")
    List<ItemValue> findByProjectIdAndItemId(@Param("projectId") Long projectId, @Param("itemId") Long itemId);

    @Query("SELECT iv FROM ItemValue iv WHERE iv.projectId = :projectId AND iv.propertyPlaceId = :propertyPlaceId")
    List<ItemValue> findByProjectIdAndPropertyPlaceId(@Param("projectId") Long projectId, 
                                                     @Param("propertyPlaceId") Long propertyPlaceId);

    @Query("SELECT iv FROM ItemValue iv WHERE iv.detectTime BETWEEN :startTime AND :endTime")
    List<ItemValue> findByDetectTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                           @Param("endTime") LocalDateTime endTime);

    @Query("SELECT iv FROM ItemValue iv WHERE " +
           "(:projectId IS NULL OR iv.projectId = :projectId) AND " +
           "(:itemId IS NULL OR iv.itemId = :itemId) AND " +
           "(:propertyPlaceId IS NULL OR iv.propertyPlaceId = :propertyPlaceId) AND " +
           "(:result IS NULL OR iv.result = :result) AND " +
           "(:status IS NULL OR iv.status = :status) AND " +
           "(:detector IS NULL OR iv.detector LIKE %:detector%)")
    Page<ItemValue> findByConditions(@Param("projectId") Long projectId,
                                    @Param("itemId") Long itemId,
                                    @Param("propertyPlaceId") Long propertyPlaceId,
                                    @Param("result") ItemValueDTO.DetectResult result,
                                    @Param("status") ItemValueDTO.ValueStatus status,
                                    @Param("detector") String detector,
                                    Pageable pageable);

    @Query("SELECT COUNT(iv) FROM ItemValue iv WHERE iv.projectId = :projectId AND iv.result = :result")
    long countByProjectIdAndResult(@Param("projectId") Long projectId, @Param("result") ItemValueDTO.DetectResult result);

    @Query("SELECT COUNT(iv) FROM ItemValue iv WHERE iv.projectId = :projectId AND iv.status = :status")
    long countByProjectIdAndStatus(@Param("projectId") Long projectId, @Param("status") ItemValueDTO.ValueStatus status);

    @Query("SELECT COUNT(iv) FROM ItemValue iv WHERE iv.propertyPlaceId = :propertyPlaceId AND iv.result = :result")
    long countByPropertyPlaceIdAndResult(@Param("propertyPlaceId") Long propertyPlaceId, 
                                        @Param("result") ItemValueDTO.DetectResult result);
}
