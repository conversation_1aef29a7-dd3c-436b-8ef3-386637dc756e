package com.dcai.detect.domain.quality;

import com.ejuetc.commons.base.entity.BaseEntity;
import com.dcai.detect.dto.QualityDTO;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.io.Serializable;
import java.time.LocalDate;

@Getter
@Entity
@Slf4j
@NoArgsConstructor
@Comment("资质管理")
@Table(name = "tb_quality", uniqueConstraints = @UniqueConstraint(name = "uk_quality_code", columnNames = {"code"}))
@Where(clause = "logic_delete = 0")
public class Quality extends BaseEntity<Quality> implements Serializable {

    @Id
    @Comment("资质主键")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", columnDefinition = "varchar(127) COMMENT '资质名称'", nullable = false)
    private String name;

    @Column(name = "code", columnDefinition = "varchar(127) COMMENT '资质代码'", nullable = false, unique = true)
    private String code;

    @Column(name = "description", columnDefinition = "text COMMENT '资质描述'")
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", columnDefinition = "varchar(31) COMMENT '资质类型'", nullable = false)
    private QualityDTO.QualityType type;

    @Enumerated(EnumType.STRING)
    @Column(name = "level", columnDefinition = "varchar(31) COMMENT '资质等级'", nullable = false)
    private QualityDTO.QualityLevel level;

    @Column(name = "issuer", columnDefinition = "varchar(127) COMMENT '颁发机构'")
    private String issuer;

    @Column(name = "valid_from", columnDefinition = "date COMMENT '有效期开始'")
    private LocalDate validFrom;

    @Column(name = "valid_to", columnDefinition = "date COMMENT '有效期结束'")
    private LocalDate validTo;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", columnDefinition = "varchar(31) COMMENT '资质状态'", nullable = false)
    private QualityDTO.QualityStatus status;

    @Column(name = "remark", columnDefinition = "text COMMENT '备注'")
    private String remark;

    public Quality(String name, String code, String description, QualityDTO.QualityType type,
                  QualityDTO.QualityLevel level, String issuer, LocalDate validFrom, LocalDate validTo, String remark) {
        this.name = name;
        this.code = code;
        this.description = description;
        this.type = type;
        this.level = level;
        this.issuer = issuer;
        this.validFrom = validFrom;
        this.validTo = validTo;
        this.remark = remark;
        this.status = QualityDTO.QualityStatus.VALID;
    }

    public void updateInfo(String name, String description, String issuer, 
                          LocalDate validFrom, LocalDate validTo, String remark) {
        this.name = name;
        this.description = description;
        this.issuer = issuer;
        this.validFrom = validFrom;
        this.validTo = validTo;
        this.remark = remark;
    }

    public void suspend() {
        this.status = QualityDTO.QualityStatus.SUSPENDED;
    }

    public void revoke() {
        this.status = QualityDTO.QualityStatus.REVOKED;
    }

    public void activate() {
        this.status = QualityDTO.QualityStatus.VALID;
    }

    public boolean isValid() {
        return QualityDTO.QualityStatus.VALID.equals(this.status) && 
               (validTo == null || validTo.isAfter(LocalDate.now()));
    }

    public boolean isExpired() {
        return validTo != null && validTo.isBefore(LocalDate.now());
    }
}
