package com.dcai.detect.domain.form;

import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.BusinessException;
import com.dcai.detect.dto.FormDTO;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Entity
@Slf4j
@NoArgsConstructor
@Comment("检测表单")
@Table(name = "tb_form", 
       uniqueConstraints = @UniqueConstraint(name = "uk_form_code", columnNames = {"code"}),
       indexes = @Index(name = "idx_form_project_id", columnList = "project_id"))
@Where(clause = "logic_delete = 0")
public class Form extends BaseEntity<Form> implements Serializable {

    @Id
    @Comment("表单主键")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", columnDefinition = "varchar(127) COMMENT '表单名称'", nullable = false)
    private String name;

    @Column(name = "code", columnDefinition = "varchar(127) COMMENT '表单编号'", nullable = false, unique = true)
    private String code;

    @Column(name = "project_id", columnDefinition = "bigint(20) COMMENT '所属项目ID'", nullable = false)
    private Long projectId;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", columnDefinition = "varchar(31) COMMENT '表单类型'", nullable = false)
    private FormDTO.FormType type;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", columnDefinition = "varchar(31) COMMENT '表单状态'", nullable = false)
    private FormDTO.FormStatus status;

    @Column(name = "operator", columnDefinition = "varchar(63) COMMENT '填写人'")
    private String operator;

    @Column(name = "fill_time", columnDefinition = "datetime COMMENT '填写时间'")
    private LocalDateTime fillTime;

    @Column(name = "reviewer", columnDefinition = "varchar(63) COMMENT '审核人'")
    private String reviewer;

    @Column(name = "review_time", columnDefinition = "datetime COMMENT '审核时间'")
    private LocalDateTime reviewTime;

    @Column(name = "remark", columnDefinition = "text COMMENT '备注'")
    private String remark;

    public Form(String name, String code, Long projectId, FormDTO.FormType type, String remark) {
        this.name = name;
        this.code = code;
        this.projectId = projectId;
        this.type = type;
        this.remark = remark;
        this.status = FormDTO.FormStatus.DRAFT;
    }

    public void fill(String operator, String remark) {
        if (!FormDTO.FormStatus.DRAFT.equals(this.status)) {
            throw new BusinessException("只有草稿状态的表单才能填写");
        }
        this.operator = operator;
        this.fillTime = LocalDateTime.now();
        this.remark = remark;
    }

    public void submit() {
        if (!FormDTO.FormStatus.DRAFT.equals(this.status)) {
            throw new BusinessException("只有草稿状态的表单才能提交");
        }
        if (this.operator == null || this.fillTime == null) {
            throw new BusinessException("表单必须先填写才能提交");
        }
        this.status = FormDTO.FormStatus.SUBMITTED;
    }

    public void approve(String reviewer, String remark) {
        if (!FormDTO.FormStatus.SUBMITTED.equals(this.status)) {
            throw new BusinessException("只有已提交的表单才能审核");
        }
        this.reviewer = reviewer;
        this.reviewTime = LocalDateTime.now();
        this.status = FormDTO.FormStatus.APPROVED;
        if (remark != null) {
            this.remark = remark;
        }
    }

    public void reject(String reviewer, String remark) {
        if (!FormDTO.FormStatus.SUBMITTED.equals(this.status)) {
            throw new BusinessException("只有已提交的表单才能驳回");
        }
        this.reviewer = reviewer;
        this.reviewTime = LocalDateTime.now();
        this.status = FormDTO.FormStatus.REJECTED;
        this.remark = remark;
    }

    public boolean isGatherForm() {
        return FormDTO.FormType.GATHER_FORM.equals(this.type);
    }

    public boolean isReportForm() {
        return FormDTO.FormType.REPORT_FORM.equals(this.type);
    }

    public boolean canEdit() {
        return FormDTO.FormStatus.DRAFT.equals(this.status) || 
               FormDTO.FormStatus.REJECTED.equals(this.status);
    }
}
