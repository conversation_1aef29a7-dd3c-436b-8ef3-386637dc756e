package com.dcai.detect.domain.training;

import com.ejuetc.commons.base.repository.JpaRepositoryImplementation;
import com.dcai.detect.dto.TrainingDTO;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface TrainingRpt extends JpaRepositoryImplementation<Training, Long> {

    @Cacheable("TrainingRpt.findByCode")
    Optional<Training> findByCode(String code);

    List<Training> findByType(TrainingDTO.TrainingType type);

    List<Training> findByLevel(TrainingDTO.TrainingLevel level);

    List<Training> findByStatus(TrainingDTO.TrainingStatus status);

    @Query("SELECT t FROM Training t WHERE " +
           "(:name IS NULL OR t.name LIKE %:name%) AND " +
           "(:type IS NULL OR t.type = :type) AND " +
           "(:level IS NULL OR t.level = :level) AND " +
           "(:status IS NULL OR t.status = :status)")
    Page<Training> findByConditions(@Param("name") String name,
                                   @Param("type") TrainingDTO.TrainingType type,
                                   @Param("level") TrainingDTO.TrainingLevel level,
                                   @Param("status") TrainingDTO.TrainingStatus status,
                                   Pageable pageable);

    @Query("SELECT t FROM Training t WHERE t.status = :status AND " +
           "t.startTime >= :startTime AND t.endTime <= :endTime")
    List<Training> findByStatusAndTimeRange(@Param("status") TrainingDTO.TrainingStatus status,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);

    @Query("SELECT t FROM Training t WHERE t.status = 'PUBLISHED' AND " +
           "(t.startTime IS NULL OR t.startTime > :currentTime)")
    List<Training> findAvailableTrainings(@Param("currentTime") LocalDateTime currentTime);

    @Query("SELECT COUNT(t) FROM Training t WHERE t.status = :status")
    long countByStatus(@Param("status") TrainingDTO.TrainingStatus status);

    @Query("SELECT t FROM Training t WHERE t.type = :type AND t.level = :level AND t.status = 'PUBLISHED'")
    List<Training> findByTypeAndLevel(@Param("type") TrainingDTO.TrainingType type,
                                     @Param("level") TrainingDTO.TrainingLevel level);
}
