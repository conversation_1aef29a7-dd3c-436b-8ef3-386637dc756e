package com.dcai.detect.domain.quality;

import com.dcai.detect.dto.QualityDTO;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface QualityRpt extends JpaRepositoryImplementation<Quality, Long> {

    @Cacheable("QualityRpt.findByCode")
    Optional<Quality> findByCode(String code);

    List<Quality> findByType(QualityDTO.QualityType type);

    List<Quality> findByStatus(QualityDTO.QualityStatus status);

    List<Quality> findByTypeAndLevel(QualityDTO.QualityType type, QualityDTO.QualityLevel level);

    @Query("SELECT q FROM Quality q WHERE q.validTo < :date AND q.status = :status")
    List<Quality> findExpiredQualities(@Param("date") LocalDate date, @Param("status") QualityDTO.QualityStatus status);

    @Query("SELECT q FROM Quality q WHERE q.validTo BETWEEN :startDate AND :endDate AND q.status = :status")
    List<Quality> findQualitiesExpiringBetween(@Param("startDate") LocalDate startDate, 
                                              @Param("endDate") LocalDate endDate,
                                              @Param("status") QualityDTO.QualityStatus status);

    @Query("SELECT q FROM Quality q WHERE " +
           "(:name IS NULL OR q.name LIKE %:name%) AND " +
           "(:type IS NULL OR q.type = :type) AND " +
           "(:level IS NULL OR q.level = :level) AND " +
           "(:status IS NULL OR q.status = :status)")
    Page<Quality> findByConditions(@Param("name") String name,
                                  @Param("type") QualityDTO.QualityType type,
                                  @Param("level") QualityDTO.QualityLevel level,
                                  @Param("status") QualityDTO.QualityStatus status,
                                  Pageable pageable);

    @Query("SELECT COUNT(q) FROM Quality q WHERE q.status = :status")
    long countByStatus(@Param("status") QualityDTO.QualityStatus status);
}
