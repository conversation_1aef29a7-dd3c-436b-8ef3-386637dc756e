package com.dcai.detect.domain.exam;

import com.ejuetc.commons.base.repository.JpaRepositoryImplementation;
import com.dcai.detect.dto.ExamDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface ExamRpt extends JpaRepositoryImplementation<Exam, Long> {

    List<Exam> findByTrainingId(Long trainingId);

    List<Exam> findByOrganId(Long organId);

    List<Exam> findByStatus(ExamDTO.ExamStatus status);

    List<Exam> findByResult(ExamDTO.ExamResult result);

    @Query("SELECT e FROM Exam e WHERE e.trainingId = :trainingId AND e.organId = :organId")
    List<Exam> findByTrainingIdAndOrganId(@Param("trainingId") Long trainingId, 
                                         @Param("organId") Long organId);

    @Query("SELECT e FROM Exam e WHERE " +
           "(:participantName IS NULL OR e.participantName LIKE %:participantName%) AND " +
           "(:organId IS NULL OR e.organId = :organId) AND " +
           "(:status IS NULL OR e.status = :status) AND " +
           "(:result IS NULL OR e.result = :result)")
    Page<Exam> findByConditions(@Param("participantName") String participantName,
                               @Param("organId") Long organId,
                               @Param("status") ExamDTO.ExamStatus status,
                               @Param("result") ExamDTO.ExamResult result,
                               Pageable pageable);

    @Query("SELECT e FROM Exam e WHERE e.status = 'CERTIFIED' AND " +
           "e.certificateValidUntil < :currentTime")
    List<Exam> findExpiredCertificates(@Param("currentTime") LocalDateTime currentTime);

    @Query("SELECT e FROM Exam e WHERE e.status = 'CERTIFIED' AND " +
           "e.certificateValidUntil BETWEEN :startTime AND :endTime")
    List<Exam> findCertificatesExpiringBetween(@Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime);

    @Query("SELECT COUNT(e) FROM Exam e WHERE e.organId = :organId AND e.status = :status")
    long countByOrganIdAndStatus(@Param("organId") Long organId, 
                                @Param("status") ExamDTO.ExamStatus status);

    @Query("SELECT COUNT(e) FROM Exam e WHERE e.trainingId = :trainingId AND e.result = :result")
    long countByTrainingIdAndResult(@Param("trainingId") Long trainingId, 
                                   @Param("result") ExamDTO.ExamResult result);

    @Query("SELECT e FROM Exam e WHERE e.organId = :organId AND e.status = 'CERTIFIED' AND " +
           "e.certificateValidUntil > :currentTime")
    List<Exam> findValidCertificatesByOrgan(@Param("organId") Long organId,
                                           @Param("currentTime") LocalDateTime currentTime);

    Optional<Exam> findByCertificateNumber(String certificateNumber);
}
