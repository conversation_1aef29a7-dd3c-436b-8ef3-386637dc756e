package com.dcai.detect.domain.item;

import com.dcai.detect.dto.ItemDTO;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface ItemRpt extends JpaRepositoryImplementation<Item, Long> {

    @Cacheable("ItemRpt.findByCode")
    Optional<Item> findByCode(String code);

    List<Item> findByDataType(ItemDTO.DataType dataType);

    List<Item> findByStatus(ItemDTO.ItemStatus status);

    @Query("SELECT i FROM Item i WHERE " +
           "(:name IS NULL OR i.name LIKE %:name%) AND " +
           "(:dataType IS NULL OR i.dataType = :dataType) AND " +
           "(:status IS NULL OR i.status = :status)")
    Page<Item> findByConditions(@Param("name") String name,
                               @Param("dataType") ItemDTO.DataType dataType,
                               @Param("status") ItemDTO.ItemStatus status,
                               Pageable pageable);

    @Query("SELECT COUNT(i) FROM Item i WHERE i.status = :status")
    long countByStatus(@Param("status") ItemDTO.ItemStatus status);
}
