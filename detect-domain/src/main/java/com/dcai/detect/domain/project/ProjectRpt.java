package com.dcai.detect.domain.project;

import com.dcai.detect.dto.ProjectDTO;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface ProjectRpt extends JpaRepositoryImplementation<Project, Long> {

    @Cacheable("ProjectRpt.findByCode")
    Optional<Project> findByCode(String code);

    List<Project> findByStatus(ProjectDTO.ProjectStatus status);

    List<Project> findByClientOrganId(Long clientOrganId);

    List<Project> findByDetectOrganId(Long detectOrganId);

    List<Project> findBySupervisionOrganId(Long supervisionOrganId);

    @Query("SELECT p FROM Project p WHERE " +
           "(:name IS NULL OR p.name LIKE %:name%) AND " +
           "(:status IS NULL OR p.status = :status) AND " +
           "(:clientOrganId IS NULL OR p.clientOrganId = :clientOrganId) AND " +
           "(:detectOrganId IS NULL OR p.detectOrganId = :detectOrganId)")
    Page<Project> findByConditions(@Param("name") String name,
                                  @Param("status") ProjectDTO.ProjectStatus status,
                                  @Param("clientOrganId") Long clientOrganId,
                                  @Param("detectOrganId") Long detectOrganId,
                                  Pageable pageable);

    @Query("SELECT COUNT(p) FROM Project p WHERE p.status = :status")
    long countByStatus(@Param("status") ProjectDTO.ProjectStatus status);
}
