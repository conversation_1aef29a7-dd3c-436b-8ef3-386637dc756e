package com.dcai.detect.domain.form;

import com.dcai.detect.dto.FormDTO;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface FormRpt extends JpaRepositoryImplementation<Form, Long> {

    @Cacheable("FormRpt.findByCode")
    Optional<Form> findByCode(String code);

    List<Form> findByProjectId(Long projectId);

    List<Form> findByProjectIdAndType(Long projectId, FormDTO.FormType type);

    List<Form> findByProjectIdAndStatus(Long projectId, FormDTO.FormStatus status);

    List<Form> findByType(FormDTO.FormType type);

    List<Form> findByStatus(FormDTO.FormStatus status);

    List<Form> findByOperator(String operator);

    List<Form> findByReviewer(String reviewer);

    @Query("SELECT f FROM Form f WHERE f.projectId = :projectId AND f.type = :type AND f.status = :status")
    List<Form> findByProjectIdAndTypeAndStatus(@Param("projectId") Long projectId,
                                              @Param("type") FormDTO.FormType type,
                                              @Param("status") FormDTO.FormStatus status);

    @Query("SELECT f FROM Form f WHERE " +
           "(:name IS NULL OR f.name LIKE %:name%) AND " +
           "(:projectId IS NULL OR f.projectId = :projectId) AND " +
           "(:type IS NULL OR f.type = :type) AND " +
           "(:status IS NULL OR f.status = :status) AND " +
           "(:operator IS NULL OR f.operator LIKE %:operator%)")
    Page<Form> findByConditions(@Param("name") String name,
                               @Param("projectId") Long projectId,
                               @Param("type") FormDTO.FormType type,
                               @Param("status") FormDTO.FormStatus status,
                               @Param("operator") String operator,
                               Pageable pageable);

    @Query("SELECT COUNT(f) FROM Form f WHERE f.projectId = :projectId AND f.status = :status")
    long countByProjectIdAndStatus(@Param("projectId") Long projectId, @Param("status") FormDTO.FormStatus status);

    @Query("SELECT COUNT(f) FROM Form f WHERE f.status = :status")
    long countByStatus(@Param("status") FormDTO.FormStatus status);
}
