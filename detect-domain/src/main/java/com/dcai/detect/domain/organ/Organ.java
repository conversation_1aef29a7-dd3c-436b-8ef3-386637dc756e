package com.dcai.detect.domain.organ;

import com.ejuetc.commons.base.entity.BaseEntity;
import com.dcai.detect.dto.OrganDTO;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.io.Serializable;

@Getter
@Entity
@Slf4j
@NoArgsConstructor
@Comment("机构信息")
@Table(name = "tb_organ", uniqueConstraints = @UniqueConstraint(name = "uk_organ_code", columnNames = {"code"}))
@Where(clause = "logic_delete = 0")
public class Organ extends BaseEntity<Organ> implements Serializable {

    @Id
    @Comment("机构主键")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", columnDefinition = "varchar(127) COMMENT '机构名称'", nullable = false)
    private String name;

    @Column(name = "code", columnDefinition = "varchar(127) COMMENT '机构代码'", nullable = false, unique = true)
    private String code;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", columnDefinition = "varchar(31) COMMENT '机构类型'", nullable = false)
    private OrganDTO.OrganType type;

    @Column(name = "phone", columnDefinition = "varchar(31) COMMENT '联系电话'")
    private String phone;

    @Column(name = "address", columnDefinition = "varchar(255) COMMENT '联系地址'")
    private String address;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", columnDefinition = "varchar(31) COMMENT '机构状态'", nullable = false)
    private OrganDTO.OrganStatus status;

    public Organ(String name, String code, OrganDTO.OrganType type, String phone, String address) {
        this.name = name;
        this.code = code;
        this.type = type;
        this.phone = phone;
        this.address = address;
        this.status = OrganDTO.OrganStatus.ACTIVE;
    }

    public void updateInfo(String name, String phone, String address) {
        this.name = name;
        this.phone = phone;
        this.address = address;
    }

    public void enable() {
        this.status = OrganDTO.OrganStatus.ACTIVE;
    }

    public void disable() {
        this.status = OrganDTO.OrganStatus.INACTIVE;
    }

    public boolean isActive() {
        return OrganDTO.OrganStatus.ACTIVE.equals(this.status);
    }
}
