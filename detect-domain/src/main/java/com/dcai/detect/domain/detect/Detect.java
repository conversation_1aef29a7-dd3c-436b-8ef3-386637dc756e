package com.dcai.detect.domain.detect;

import com.ejuetc.commons.base.entity.BaseEntity;
import com.dcai.detect.dto.DetectDTO;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Entity
@Slf4j
@NoArgsConstructor
@Comment("检测套餐")
@Table(name = "tb_detect", uniqueConstraints = @UniqueConstraint(name = "uk_detect_code", columnNames = {"code"}))
@Where(clause = "logic_delete = 0")
public class Detect extends BaseEntity<Detect> implements Serializable {

    @Id
    @Comment("套餐主键")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", columnDefinition = "varchar(127) COMMENT '套餐名称'", nullable = false)
    private String name;

    @Column(name = "code", columnDefinition = "varchar(127) COMMENT '套餐代码'", nullable = false, unique = true)
    private String code;

    @Column(name = "description", columnDefinition = "text COMMENT '套餐描述'")
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", columnDefinition = "varchar(31) COMMENT '检测类型'", nullable = false)
    private DetectDTO.DetectType type;

    @Column(name = "price", columnDefinition = "decimal(10,2) COMMENT '套餐价格'")
    private BigDecimal price;

    @Column(name = "cycle_days", columnDefinition = "int COMMENT '检测周期(天)'")
    private Integer cycleDays;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", columnDefinition = "varchar(31) COMMENT '套餐状态'", nullable = false)
    private DetectDTO.DetectStatus status;

    @Column(name = "require_quality", columnDefinition = "tinyint(1) COMMENT '是否需要资质'")
    private Boolean requireQuality;

    @Column(name = "remark", columnDefinition = "text COMMENT '备注'")
    private String remark;

    public Detect(String name, String code, String description, DetectDTO.DetectType type,
                  BigDecimal price, Integer cycleDays, Boolean requireQuality, String remark) {
        this.name = name;
        this.code = code;
        this.description = description;
        this.type = type;
        this.price = price;
        this.cycleDays = cycleDays;
        this.requireQuality = requireQuality;
        this.remark = remark;
        this.status = DetectDTO.DetectStatus.DRAFT;
    }

    public void updateInfo(String name, String description, BigDecimal price, 
                          Integer cycleDays, Boolean requireQuality, String remark) {
        this.name = name;
        this.description = description;
        this.price = price;
        this.cycleDays = cycleDays;
        this.requireQuality = requireQuality;
        this.remark = remark;
    }

    public void activate() {
        this.status = DetectDTO.DetectStatus.ACTIVE;
    }

    public void deactivate() {
        this.status = DetectDTO.DetectStatus.INACTIVE;
    }

    public boolean isActive() {
        return DetectDTO.DetectStatus.ACTIVE.equals(this.status);
    }

    public boolean needQuality() {
        return Boolean.TRUE.equals(this.requireQuality);
    }
}
