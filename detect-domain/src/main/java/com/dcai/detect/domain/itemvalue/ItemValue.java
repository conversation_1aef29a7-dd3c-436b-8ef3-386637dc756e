package com.dcai.detect.domain.itemvalue;

import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.BusinessException;
import com.dcai.detect.dto.ItemValueDTO;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Where;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Entity
@Slf4j
@NoArgsConstructor
@Comment("项目值")
@Table(name = "tb_item_value", 
       uniqueConstraints = @UniqueConstraint(name = "uk_item_value", columnNames = {"project_id", "item_id", "property_place_id", "location"}),
       indexes = {
           @Index(name = "idx_item_value_project_id", columnList = "project_id"),
           @Index(name = "idx_item_value_item_id", columnList = "item_id"),
           @Index(name = "idx_item_value_property_place_id", columnList = "property_place_id")
       })
@Where(clause = "logic_delete = 0")
public class ItemValue extends BaseEntity<ItemValue> implements Serializable {

    @Id
    @Comment("项目值主键")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "project_id", columnDefinition = "bigint(20) COMMENT '所属项目ID'", nullable = false)
    private Long projectId;

    @Column(name = "item_id", columnDefinition = "bigint(20) COMMENT '检测项ID'", nullable = false)
    private Long itemId;

    @Column(name = "property_place_id", columnDefinition = "bigint(20) COMMENT '物业场所ID'", nullable = false)
    private Long propertyPlaceId;

    @Column(name = "value", columnDefinition = "text COMMENT '检测值'")
    private String value;

    @Enumerated(EnumType.STRING)
    @Column(name = "result", columnDefinition = "varchar(31) COMMENT '检测结果'")
    private ItemValueDTO.DetectResult result;

    @Column(name = "location", columnDefinition = "varchar(255) COMMENT '检测位置'")
    private String location;

    @Column(name = "detect_time", columnDefinition = "datetime COMMENT '检测时间'")
    private LocalDateTime detectTime;

    @Column(name = "detector", columnDefinition = "varchar(63) COMMENT '检测人员'")
    private String detector;

    @Column(name = "equipment", columnDefinition = "varchar(127) COMMENT '检测设备'")
    private String equipment;

    @Column(name = "environment", columnDefinition = "text COMMENT '环境条件'")
    private String environment;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", columnDefinition = "varchar(31) COMMENT '数据状态'", nullable = false)
    private ItemValueDTO.ValueStatus status;

    @Column(name = "remark", columnDefinition = "text COMMENT '备注'")
    private String remark;

    public ItemValue(Long projectId, Long itemId, Long propertyPlaceId, String value,
                    String location, LocalDateTime detectTime, String detector,
                    String equipment, String environment, String remark) {
        this.projectId = projectId;
        this.itemId = itemId;
        this.propertyPlaceId = propertyPlaceId;
        this.value = value;
        this.location = location;
        this.detectTime = detectTime;
        this.detector = detector;
        this.equipment = equipment;
        this.environment = environment;
        this.remark = remark;
        this.status = ItemValueDTO.ValueStatus.DRAFT;
        this.result = ItemValueDTO.DetectResult.PENDING;
    }

    public void updateValue(String value, String location, LocalDateTime detectTime,
                           String detector, String equipment, String environment, String remark) {
        if (!canEdit()) {
            throw new BusinessException("当前状态不允许编辑");
        }
        this.value = value;
        this.location = location;
        this.detectTime = detectTime;
        this.detector = detector;
        this.equipment = equipment;
        this.environment = environment;
        this.remark = remark;
    }

    public void confirm() {
        if (!ItemValueDTO.ValueStatus.DRAFT.equals(this.status)) {
            throw new BusinessException("只有草稿状态的数据才能确认");
        }
        this.status = ItemValueDTO.ValueStatus.CONFIRMED;
    }

    public void review(ItemValueDTO.DetectResult result, String remark) {
        if (!ItemValueDTO.ValueStatus.CONFIRMED.equals(this.status)) {
            throw new BusinessException("只有已确认的数据才能审核");
        }
        this.result = result;
        this.status = ItemValueDTO.ValueStatus.REVIEWED;
        if (remark != null) {
            this.remark = remark;
        }
    }

    public void invalidate(String remark) {
        this.status = ItemValueDTO.ValueStatus.INVALID;
        this.remark = remark;
    }

    public boolean canEdit() {
        return ItemValueDTO.ValueStatus.DRAFT.equals(this.status);
    }

    public boolean isQualified() {
        return ItemValueDTO.DetectResult.QUALIFIED.equals(this.result);
    }

    public boolean isUnqualified() {
        return ItemValueDTO.DetectResult.UNQUALIFIED.equals(this.result);
    }

    /**
     * 判断是否为重大异常
     * 重大异常的判定标准：
     * 1. 检测结果为不合格
     * 2. 涉及安全相关的检测项（结构、电气、消防）
     * 3. 数值严重超标（超出标准范围50%以上）
     */
    public boolean isMajorAnomaly() {
        if (!ItemValueDTO.DetectResult.UNQUALIFIED.equals(this.result)) {
            return false;
        }

        // 检查是否为安全相关检测项（需要根据实际业务逻辑调整）
        if (isSafetyRelatedItem()) {
            return true;
        }

        // 检查数值是否严重超标
        return isSeverelyOutOfRange();
    }

    /**
     * 判断是否需要触发专项检测
     */
    public boolean needSpecializedDetection() {
        return isMajorAnomaly() ||
               ItemValueDTO.DetectResult.EXCEPTION.equals(this.result);
    }

    /**
     * 获取异常等级描述
     */
    public String getAnomalyLevelDescription() {
        if (ItemValueDTO.DetectResult.QUALIFIED.equals(this.result)) {
            return "正常";
        } else if (isMajorAnomaly()) {
            return "重大异常";
        } else if (ItemValueDTO.DetectResult.UNQUALIFIED.equals(this.result)) {
            return "轻微异常";
        } else if (ItemValueDTO.DetectResult.EXCEPTION.equals(this.result)) {
            return "检测异常";
        } else {
            return "待判定";
        }
    }

    /**
     * 生成专项检测建议
     */
    public String generateSpecializedDetectionSuggestion() {
        if (!needSpecializedDetection()) {
            return null;
        }

        StringBuilder suggestion = new StringBuilder();
        suggestion.append("建议进行专项检测：");

        if (isMajorAnomaly()) {
            suggestion.append("检测结果显示重大异常，");
            if (isSafetyRelatedItem()) {
                suggestion.append("涉及安全相关项目，");
            }
            suggestion.append("建议立即安排专业机构进行深入检测。");
        } else if (ItemValueDTO.DetectResult.EXCEPTION.equals(this.result)) {
            suggestion.append("检测过程中出现异常，建议重新检测或更换检测方法。");
        }

        return suggestion.toString();
    }

    private boolean isSafetyRelatedItem() {
        // 这里需要根据实际的检测项类型或代码来判断是否为安全相关项目
        return this.remark != null &&
               (this.remark.contains("结构") ||
                this.remark.contains("电气") ||
                this.remark.contains("消防") ||
                this.remark.contains("安全"));
    }

    private boolean isSeverelyOutOfRange() {
        // 这里需要根据实际的数值和标准范围来判断
        // 简化实现：如果备注中包含"严重超标"等关键词
        return this.remark != null &&
               (this.remark.contains("严重超标") ||
                this.remark.contains("超标50%") ||
                this.remark.contains("严重异常"));
    }
}
