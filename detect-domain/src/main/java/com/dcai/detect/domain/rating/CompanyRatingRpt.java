package com.dcai.detect.domain.rating;

import com.ejuetc.commons.base.repository.JpaRepositoryImplementation;
import com.dcai.detect.dto.CompanyRatingDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public interface CompanyRatingRpt extends JpaRepositoryImplementation<CompanyRating, Long> {

    List<CompanyRating> findByPropertyCompanyId(Long propertyCompanyId);

    List<CompanyRating> findByRatingLevel(CompanyRatingDTO.RatingLevel ratingLevel);

    List<CompanyRating> findByStatus(CompanyRatingDTO.RatingStatus status);

    @Query("SELECT cr FROM CompanyRating cr WHERE cr.propertyCompanyId = :propertyCompanyId AND " +
           "cr.ratingYear = :ratingYear ORDER BY cr.ratingQuarter DESC")
    List<CompanyRating> findByCompanyAndYear(@Param("propertyCompanyId") Long propertyCompanyId,
                                           @Param("ratingYear") Integer ratingYear);

    @Query("SELECT cr FROM CompanyRating cr WHERE cr.propertyCompanyId = :propertyCompanyId AND " +
           "cr.ratingYear = :ratingYear AND cr.ratingQuarter = :ratingQuarter")
    Optional<CompanyRating> findByCompanyAndPeriod(@Param("propertyCompanyId") Long propertyCompanyId,
                                                  @Param("ratingYear") Integer ratingYear,
                                                  @Param("ratingQuarter") Integer ratingQuarter);

    @Query("SELECT cr FROM CompanyRating cr WHERE " +
           "(:propertyCompanyId IS NULL OR cr.propertyCompanyId = :propertyCompanyId) AND " +
           "(:ratingYear IS NULL OR cr.ratingYear = :ratingYear) AND " +
           "(:ratingQuarter IS NULL OR cr.ratingQuarter = :ratingQuarter) AND " +
           "(:ratingLevel IS NULL OR cr.ratingLevel = :ratingLevel) AND " +
           "(:status IS NULL OR cr.status = :status)")
    Page<CompanyRating> findByConditions(@Param("propertyCompanyId") Long propertyCompanyId,
                                       @Param("ratingYear") Integer ratingYear,
                                       @Param("ratingQuarter") Integer ratingQuarter,
                                       @Param("ratingLevel") CompanyRatingDTO.RatingLevel ratingLevel,
                                       @Param("status") CompanyRatingDTO.RatingStatus status,
                                       Pageable pageable);

    @Query("SELECT cr FROM CompanyRating cr WHERE cr.ratingYear = :ratingYear AND " +
           "cr.ratingQuarter = :ratingQuarter AND cr.status = 'PUBLISHED' " +
           "ORDER BY cr.totalScore DESC")
    List<CompanyRating> findRankingByPeriod(@Param("ratingYear") Integer ratingYear,
                                          @Param("ratingQuarter") Integer ratingQuarter);

    @Query("SELECT cr FROM CompanyRating cr WHERE cr.totalScore >= :minScore AND " +
           "cr.totalScore <= :maxScore AND cr.status = 'PUBLISHED'")
    List<CompanyRating> findByScoreRange(@Param("minScore") BigDecimal minScore,
                                       @Param("maxScore") BigDecimal maxScore);

    @Query("SELECT COUNT(cr) FROM CompanyRating cr WHERE cr.ratingLevel = :ratingLevel AND " +
           "cr.ratingYear = :ratingYear AND cr.status = 'PUBLISHED'")
    long countByLevelAndYear(@Param("ratingLevel") CompanyRatingDTO.RatingLevel ratingLevel,
                            @Param("ratingYear") Integer ratingYear);

    @Query("SELECT AVG(cr.totalScore) FROM CompanyRating cr WHERE cr.ratingYear = :ratingYear AND " +
           "cr.status = 'PUBLISHED'")
    BigDecimal getAverageScoreByYear(@Param("ratingYear") Integer ratingYear);

    @Query("SELECT cr FROM CompanyRating cr WHERE cr.propertyCompanyId = :propertyCompanyId " +
           "ORDER BY cr.ratingYear DESC, cr.ratingQuarter DESC")
    List<CompanyRating> findLatestByCompany(@Param("propertyCompanyId") Long propertyCompanyId,
                                          Pageable pageable);

    @Query("SELECT cr FROM CompanyRating cr WHERE cr.ratingLevel IN ('POOR', 'UNQUALIFIED') AND " +
           "cr.status = 'PUBLISHED' AND cr.ratingYear = :ratingYear")
    List<CompanyRating> findCompaniesNeedImprovement(@Param("ratingYear") Integer ratingYear);
}
