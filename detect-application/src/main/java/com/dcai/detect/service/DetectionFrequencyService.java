package com.dcai.detect.service;

import com.dcai.detect.domain.propertyplace.PropertyPlace;
import com.dcai.detect.domain.propertyplace.PropertyPlaceRpt;
import com.dcai.detect.domain.itemvalue.ItemValue;
import com.dcai.detect.domain.itemvalue.ItemValueRpt;
import com.dcai.detect.domain.project.Project;
import com.dcai.detect.domain.project.ProjectRpt;
import com.dcai.detect.dto.ItemValueDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 检测频率管理服务
 * 负责智能检测频率计算、异常检测识别、专项检测触发等功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DetectionFrequencyService {

    private final PropertyPlaceRpt propertyPlaceRpt;
    private final ItemValueRpt itemValueRpt;
    private final ProjectRpt projectRpt;

    /**
     * 获取物业场所的检测频率信息
     */
    public DetectionFrequencyInfo getDetectionFrequency(Long propertyPlaceId) {
        PropertyPlace propertyPlace = propertyPlaceRpt.findById(propertyPlaceId)
                .orElseThrow(() -> new RuntimeException("物业场所不存在"));

        int cycleDays = propertyPlace.calculateDetectionCycleDays();
        String description = propertyPlace.getDetectionFrequencyDescription();
        boolean needHigherFrequency = propertyPlace.needHigherFrequency();

        return DetectionFrequencyInfo.builder()
                .propertyPlaceId(propertyPlaceId)
                .cycleDays(cycleDays)
                .description(description)
                .needHigherFrequency(needHigherFrequency)
                .buildYear(propertyPlace.getBuildYear())
                .build();
    }

    /**
     * 批量检查物业场所的检测频率
     */
    public List<DetectionFrequencyInfo> batchCheckDetectionFrequency(List<Long> propertyPlaceIds) {
        return propertyPlaceIds.stream()
                .map(this::getDetectionFrequency)
                .collect(Collectors.toList());
    }

    /**
     * 检测异常分析
     */
    @Transactional(readOnly = true)
    public AnomalyAnalysisResult analyzeAnomalies(Long projectId) {
        Project project = projectRpt.findById(projectId)
                .orElseThrow(() -> new RuntimeException("项目不存在"));

        List<ItemValue> itemValues = itemValueRpt.findByProjectId(projectId);
        
        // 统计各类异常
        long totalItems = itemValues.size();
        long qualifiedItems = itemValues.stream()
                .mapToLong(item -> item.isQualified() ? 1 : 0)
                .sum();
        long unqualifiedItems = itemValues.stream()
                .mapToLong(item -> item.isUnqualified() ? 1 : 0)
                .sum();
        long majorAnomalies = itemValues.stream()
                .mapToLong(item -> item.isMajorAnomaly() ? 1 : 0)
                .sum();
        long needSpecializedDetection = itemValues.stream()
                .mapToLong(item -> item.needSpecializedDetection() ? 1 : 0)
                .sum();

        // 生成专项检测建议
        List<String> specializedDetectionSuggestions = itemValues.stream()
                .filter(ItemValue::needSpecializedDetection)
                .map(ItemValue::generateSpecializedDetectionSuggestion)
                .filter(suggestion -> suggestion != null)
                .collect(Collectors.toList());

        // 异常等级分布
        Map<String, Long> anomalyLevelDistribution = itemValues.stream()
                .collect(Collectors.groupingBy(
                        ItemValue::getAnomalyLevelDescription,
                        Collectors.counting()
                ));

        return AnomalyAnalysisResult.builder()
                .projectId(projectId)
                .totalItems(totalItems)
                .qualifiedItems(qualifiedItems)
                .unqualifiedItems(unqualifiedItems)
                .majorAnomalies(majorAnomalies)
                .needSpecializedDetection(needSpecializedDetection)
                .qualificationRate(totalItems > 0 ? (double) qualifiedItems / totalItems * 100 : 0)
                .majorAnomalyRate(totalItems > 0 ? (double) majorAnomalies / totalItems * 100 : 0)
                .specializedDetectionSuggestions(specializedDetectionSuggestions)
                .anomalyLevelDistribution(anomalyLevelDistribution)
                .analysisTime(LocalDateTime.now())
                .build();
    }

    /**
     * 自动触发专项检测
     */
    @Transactional
    public SpecializedDetectionTriggerResult triggerSpecializedDetection(Long projectId) {
        AnomalyAnalysisResult analysisResult = analyzeAnomalies(projectId);
        
        if (analysisResult.getNeedSpecializedDetection() == 0) {
            return SpecializedDetectionTriggerResult.builder()
                    .projectId(projectId)
                    .triggered(false)
                    .reason("未发现需要专项检测的异常")
                    .build();
        }

        // 这里可以实现自动创建专项检测项目的逻辑
        // 例如：创建新的检测项目，选择专项检测套餐等
        
        log.info("项目 {} 触发专项检测，发现 {} 个重大异常", 
                projectId, analysisResult.getMajorAnomalies());

        return SpecializedDetectionTriggerResult.builder()
                .projectId(projectId)
                .triggered(true)
                .reason(String.format("发现 %d 个重大异常，需要专项检测", 
                        analysisResult.getMajorAnomalies()))
                .majorAnomalies(analysisResult.getMajorAnomalies())
                .suggestions(analysisResult.getSpecializedDetectionSuggestions())
                .triggerTime(LocalDateTime.now())
                .build();
    }

    /**
     * 获取需要提高检测频率的物业场所
     */
    public List<PropertyPlace> getPropertiesNeedHigherFrequency() {
        return propertyPlaceRpt.findAll().stream()
                .filter(PropertyPlace::needHigherFrequency)
                .collect(Collectors.toList());
    }

    // 内部数据类
    @lombok.Data
    @lombok.Builder
    public static class DetectionFrequencyInfo {
        private Long propertyPlaceId;
        private int cycleDays;
        private String description;
        private boolean needHigherFrequency;
        private Integer buildYear;
    }

    @lombok.Data
    @lombok.Builder
    public static class AnomalyAnalysisResult {
        private Long projectId;
        private long totalItems;
        private long qualifiedItems;
        private long unqualifiedItems;
        private long majorAnomalies;
        private long needSpecializedDetection;
        private double qualificationRate;
        private double majorAnomalyRate;
        private List<String> specializedDetectionSuggestions;
        private Map<String, Long> anomalyLevelDistribution;
        private LocalDateTime analysisTime;
    }

    @lombok.Data
    @lombok.Builder
    public static class SpecializedDetectionTriggerResult {
        private Long projectId;
        private boolean triggered;
        private String reason;
        private long majorAnomalies;
        private List<String> suggestions;
        private LocalDateTime triggerTime;
    }
}
