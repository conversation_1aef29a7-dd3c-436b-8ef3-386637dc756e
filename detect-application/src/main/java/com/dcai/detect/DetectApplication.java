package com.dcai.detect;

import com.ejuetc.commons.base.application.BaseApplication;
import com.ejuetc.commons.base.filter.ClearJsonTypeFilter;
import jakarta.servlet.Filter;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@SpringBootApplication(scanBasePackages = {"com.ejuetc.**", "com.dcai.**"})
@ServletComponentScan(basePackages = {"com.ejuetc.**", "com.dcai.**"})
@EnableJpaRepositories(basePackages = {"com.ejuetc.**", "com.dcai.**"})
@EntityScan(basePackages = {"com.ejuetc.**", "com.dcai.**"})
@EnableDiscoveryClient
@EnableFeignClients({"com.ejuetc.**", "com.dcai.**"})
@EnableScheduling
@EnableAspectJAutoProxy
@EnableAsync
@ComponentScan(basePackages = {"com.ejuetc.**", "com.dcai.**"})
public class DetectApplication extends BaseApplication {

    public static void main(String[] args) {
        SpringApplication.run(DetectApplication.class, args);
    }

    @Bean
    public FilterRegistrationBean<Filter> clearJsonTypeFilter() {
        FilterRegistrationBean<Filter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new ClearJsonTypeFilter());
        registrationBean.addUrlPatterns("/*");
        registrationBean.setOrder(1);
        return registrationBean;
    }
}
