package com.dcai.detect.apiImpl;

import com.ejuetc.commons.base.response.ApiResponse;
import com.dcai.detect.api.OrganApi;
import com.dcai.detect.domain.organ.Organ;
import com.dcai.detect.domain.organ.OrganRpt;
import com.dcai.detect.dto.OrganDTO;
import com.dcai.detect.pro.OrganPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class OrganApiImpl implements OrganApi {

    private final OrganRpt organRpt;

    @Override
    public ApiResponse<OrganDTO> createOrgan(OrganPO organPO) {
        Organ organ = new Organ(
                organPO.getName(),
                organPO.getCode(),
                organPO.getType(),
                organPO.getPhone(),
                organPO.getAddress()
        ).save();
        return succ(convert2DTO(organ, new OrganDTO()));
    }

    @Override
    public ApiResponse<OrganDTO> updateOrgan(Long id, OrganPO organPO) {
        Organ organ = organRpt.findById(id).orElseThrow(() -> new RuntimeException("机构不存在"));
        organ.updateInfo(organPO.getName(), organPO.getPhone(), organPO.getAddress());
        organ.merge();
        return succ(convert2DTO(organ, new OrganDTO()));
    }

    @Override
    public ApiResponse<Void> deleteOrgan(Long id) {
        Organ organ = organRpt.findById(id).orElseThrow(() -> new RuntimeException("机构不存在"));
        organ.logicDelete();
        return succ();
    }

    @Override
    public ApiResponse<OrganDTO> getOrganById(Long id) {
        Organ organ = organRpt.findById(id).orElseThrow(() -> new RuntimeException("机构不存在"));
        return succ(convert2DTO(organ, new OrganDTO()));
    }

    @Override
    public ApiResponse<OrganDTO> getOrganByCode(String code) {
        Organ organ = organRpt.findByCode(code).orElseThrow(() -> new RuntimeException("机构不存在"));
        return succ(convert2DTO(organ, new OrganDTO()));
    }

    @Override
    public ApiResponse<List<OrganDTO>> listOrgans(String name, OrganDTO.OrganType type, 
                                                  OrganDTO.OrganStatus status, Integer page, Integer size) {
        Page<Organ> organPage = organRpt.findByConditions(name, type, status, PageRequest.of(page - 1, size));
        List<OrganDTO> organDTOs = organPage.getContent().stream()
                .map(organ -> convert2DTO(organ, new OrganDTO()))
                .toList();
        return succ(organDTOs);
    }

    @Override
    public ApiResponse<Void> enableOrgan(Long id) {
        Organ organ = organRpt.findById(id).orElseThrow(() -> new RuntimeException("机构不存在"));
        organ.enable();
        organ.merge();
        return succ();
    }

    @Override
    public ApiResponse<Void> disableOrgan(Long id) {
        Organ organ = organRpt.findById(id).orElseThrow(() -> new RuntimeException("机构不存在"));
        organ.disable();
        organ.merge();
        return succ();
    }
}
