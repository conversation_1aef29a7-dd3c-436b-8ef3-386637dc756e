spring:
  profiles:
    active: local
  application:
    business: dcai
    name: detect
  servlet:
    multipart:
      enabled: true
      max-file-size: 500MB
      max-request-size: 1000MB
  jackson:
    serialization:
      indent_output: true
  jpa:
    properties:
      hibernate:
        default_batch_fetch_size: 100
  datasource:
    hikari:
      transaction-isolation: TRANSACTION_READ_COMMITTED
  data:
    redis:
      host: r-uf6zvmt0tsgr8dpl0d.redis.rds.aliyuncs.com
      password: 8KNkiy^*6tgJbnft#5dSu8Btej
      port: 6379
      timeout: 5000
      jedis:
        pool:
          max-active: 120
          max-wait: 3000
          min-idle: 5
          max-idle: 100
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
server:
  port: 8022
  servlet:
    context-path: /${spring.application.name}
    session:
      timeout: 1800s
  tomcat:
    uri-encoding: UTF-8
    threads:
      max: 200
    basedir: /tmp
logging:
  level:
    com.dcai.detect: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/detect.log
