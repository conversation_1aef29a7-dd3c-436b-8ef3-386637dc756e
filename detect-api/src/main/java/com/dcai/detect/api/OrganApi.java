package com.dcai.detect.api;

import com.ejuetc.commons.base.response.ApiResponse;
import com.dcai.detect.dto.OrganDTO;
import com.dcai.detect.pro.OrganPO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "机构管理", description = "机构管理相关接口")
@RequestMapping("/api/organ")
public interface OrganApi {
    
    @Operation(summary = "创建机构")
    @PostMapping("/create")
    ApiResponse<OrganDTO> createOrgan(@RequestBody OrganPO organPO);
    
    @Operation(summary = "更新机构")
    @PutMapping("/update/{id}")
    ApiResponse<OrganDTO> updateOrgan(@PathVariable Long id, @RequestBody OrganPO organPO);
    
    @Operation(summary = "删除机构")
    @DeleteMapping("/delete/{id}")
    ApiResponse<Void> deleteOrgan(@PathVariable Long id);
    
    @Operation(summary = "根据ID查询机构")
    @GetMapping("/get/{id}")
    ApiResponse<OrganDTO> getOrganById(@PathVariable Long id);
    
    @Operation(summary = "根据代码查询机构")
    @GetMapping("/getByCode/{code}")
    ApiResponse<OrganDTO> getOrganByCode(@PathVariable String code);
    
    @Operation(summary = "查询机构列表")
    @GetMapping("/list")
    ApiResponse<List<OrganDTO>> listOrgans(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) OrganDTO.OrganType type,
            @RequestParam(required = false) OrganDTO.OrganStatus status,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size
    );
    
    @Operation(summary = "启用机构")
    @PostMapping("/enable/{id}")
    ApiResponse<Void> enableOrgan(@PathVariable Long id);
    
    @Operation(summary = "停用机构")
    @PostMapping("/disable/{id}")
    ApiResponse<Void> disableOrgan(@PathVariable Long id);
}
