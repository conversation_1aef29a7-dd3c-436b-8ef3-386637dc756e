package com.dcai.detect.api;

import com.ejuetc.commons.base.response.ApiResponse;
import com.dcai.detect.dto.ProjectDTO;
import com.dcai.detect.pro.ProjectPO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "项目管理", description = "检测项目管理相关接口")
@RequestMapping("/api/project")
public interface ProjectApi {
    
    @Operation(summary = "创建项目")
    @PostMapping("/create")
    ApiResponse<ProjectDTO> createProject(@RequestBody ProjectPO projectPO);
    
    @Operation(summary = "更新项目")
    @PutMapping("/update/{id}")
    ApiResponse<ProjectDTO> updateProject(@PathVariable Long id, @RequestBody ProjectPO projectPO);
    
    @Operation(summary = "删除项目")
    @DeleteMapping("/delete/{id}")
    ApiResponse<Void> deleteProject(@PathVariable Long id);
    
    @Operation(summary = "根据ID查询项目")
    @GetMapping("/get/{id}")
    ApiResponse<ProjectDTO> getProjectById(@PathVariable Long id);
    
    @Operation(summary = "根据代码查询项目")
    @GetMapping("/getByCode/{code}")
    ApiResponse<ProjectDTO> getProjectByCode(@PathVariable String code);
    
    @Operation(summary = "查询项目列表")
    @GetMapping("/list")
    ApiResponse<List<ProjectDTO>> listProjects(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) ProjectDTO.ProjectStatus status,
            @RequestParam(required = false) Long clientOrganId,
            @RequestParam(required = false) Long detectOrganId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size
    );
    
    @Operation(summary = "审批项目")
    @PostMapping("/approve/{id}")
    ApiResponse<Void> approveProject(@PathVariable Long id);
    
    @Operation(summary = "开始项目")
    @PostMapping("/start/{id}")
    ApiResponse<Void> startProject(@PathVariable Long id);
    
    @Operation(summary = "完成项目")
    @PostMapping("/complete/{id}")
    ApiResponse<Void> completeProject(@PathVariable Long id);
    
    @Operation(summary = "取消项目")
    @PostMapping("/cancel/{id}")
    ApiResponse<Void> cancelProject(@PathVariable Long id);
}
