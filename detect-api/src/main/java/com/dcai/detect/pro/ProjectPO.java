package com.dcai.detect.pro;

import com.dcai.detect.dto.ProjectDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "项目参数对象")
public class ProjectPO {
    
    @Schema(description = "项目名称", required = true)
    @NotBlank(message = "项目名称不能为空")
    private String name;
    
    @Schema(description = "项目编号", required = true)
    @NotBlank(message = "项目编号不能为空")
    private String code;
    
    @Schema(description = "项目描述")
    private String description;
    
    @Schema(description = "项目状态")
    private ProjectDTO.ProjectStatus status;
    
    @Schema(description = "社区信息")
    private String community;
    
    @Schema(description = "委托机构ID", required = true)
    @NotNull(message = "委托机构不能为空")
    private Long clientOrganId;
    
    @Schema(description = "检测机构ID", required = true)
    @NotNull(message = "检测机构不能为空")
    private Long detectOrganId;
    
    @Schema(description = "监管机构ID")
    private Long supervisionOrganId;
    
    @Schema(description = "计划开始时间")
    private LocalDateTime plannedStartTime;
    
    @Schema(description = "计划结束时间")
    private LocalDateTime plannedEndTime;
}
