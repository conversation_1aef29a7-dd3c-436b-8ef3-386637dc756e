package com.dcai.detect.pro;

import com.dcai.detect.dto.OrganDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Schema(name = "机构参数对象")
public class OrganPO {
    
    @Schema(description = "机构名称", required = true)
    @NotBlank(message = "机构名称不能为空")
    private String name;
    
    @Schema(description = "机构代码", required = true)
    @NotBlank(message = "机构代码不能为空")
    private String code;
    
    @Schema(description = "机构类型", required = true)
    @NotNull(message = "机构类型不能为空")
    private OrganDTO.OrganType type;
    
    @Schema(description = "联系电话")
    private String phone;
    
    @Schema(description = "联系地址")
    private String address;
    
    @Schema(description = "机构状态")
    private OrganDTO.OrganStatus status;
}
