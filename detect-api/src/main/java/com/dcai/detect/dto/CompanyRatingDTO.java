package com.dcai.detect.dto;

import com.ejuetc.commons.base.dto.BaseDTO;
import com.ejuetc.commons.base.enums.TitleEnum;
import com.ejuetc.commons.query.annotation.QueryDomain;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.dcai.detect.domain.rating.CompanyRating")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "物业公司评级")
public class CompanyRatingDTO extends BaseDTO<CompanyRatingDTO> {

    @Schema(description = "物业公司ID")
    private Long propertyCompanyId;

    @Schema(description = "评级年度")
    private Integer ratingYear;

    @Schema(description = "评级季度(1-4)")
    private Integer ratingQuarter;

    @Schema(description = "评级等级")
    private RatingLevel ratingLevel;

    @Schema(description = "总分")
    private BigDecimal totalScore;

    @Schema(description = "检测合规分数")
    private BigDecimal detectionComplianceScore;

    @Schema(description = "质量分数")
    private BigDecimal qualityScore;

    @Schema(description = "及时性分数")
    private BigDecimal timelinessScore;

    @Schema(description = "配合度分数")
    private BigDecimal cooperationScore;

    @Schema(description = "总项目数")
    private Integer totalProjects;

    @Schema(description = "完成项目数")
    private Integer completedProjects;

    @Schema(description = "合格项目数")
    private Integer qualifiedProjects;

    @Schema(description = "逾期项目数")
    private Integer overdueProjects;

    @Schema(description = "重大问题数")
    private Integer majorIssues;

    @Schema(description = "评级日期")
    private LocalDate ratingDate;

    @Schema(description = "评级员")
    private String evaluator;

    @Schema(description = "评级说明")
    private String evaluationNotes;

    @Schema(description = "评级状态")
    private RatingStatus status;

    @Schema(description = "改进建议")
    private String improvementSuggestions;

    // 关联对象信息
    @Schema(description = "物业公司信息")
    private OrganDTO propertyCompany;

    @Getter
    public enum RatingLevel implements TitleEnum {
        EXCELLENT("优秀"),
        GOOD("良好"),
        AVERAGE("一般"),
        POOR("较差"),
        UNQUALIFIED("不合格");

        private final String title;

        RatingLevel(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }

    @Getter
    public enum RatingStatus implements TitleEnum {
        DRAFT("草稿"),
        APPROVED("已审批"),
        PUBLISHED("已发布"),
        REJECTED("已驳回");

        private final String title;

        RatingStatus(String title) {
            this.title = title;
        }

        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
}
