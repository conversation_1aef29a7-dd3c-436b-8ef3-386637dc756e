package com.dcai.detect.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.dcai.detect.domain.form.Form")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "检测表单")
public class FormDTO extends BaseDTO<FormDTO> {
    
    @Schema(description = "表单名称")
    private String name;
    
    @Schema(description = "表单编号")
    private String code;
    
    @Schema(description = "所属项目ID")
    private Long projectId;
    
    @Schema(description = "表单类型")
    private FormType type;
    
    @Schema(description = "表单状态")
    private FormStatus status;
    
    @Schema(description = "填写人")
    private String operator;
    
    @Schema(description = "填写时间")
    private LocalDateTime fillTime;
    
    @Schema(description = "审核人")
    private String reviewer;
    
    @Schema(description = "审核时间")
    private LocalDateTime reviewTime;
    
    @Schema(description = "备注")
    private String remark;
    
    @Getter
    public enum FormType implements TitleEnum {
        GATHER_FORM("采集表单"),
        REPORT_FORM("报告表单");
        
        private final String title;
        
        FormType(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
    
    @Getter
    public enum FormStatus implements TitleEnum {
        DRAFT("草稿"),
        SUBMITTED("已提交"),
        APPROVED("已审核"),
        REJECTED("已驳回");
        
        private final String title;
        
        FormStatus(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
}
