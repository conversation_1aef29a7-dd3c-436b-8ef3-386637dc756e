package com.dcai.detect.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.dcai.detect.domain.device.Device")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "检测设备")
public class DeviceDTO extends BaseDTO<DeviceDTO> {
    
    @Schema(description = "设备名称")
    private String name;
    
    @Schema(description = "设备编号")
    private String code;
    
    @Schema(description = "设备类型")
    private DeviceType type;
    
    @Schema(description = "设备型号")
    private String model;
    
    @Schema(description = "制造商")
    private String manufacturer;
    
    @Schema(description = "设备状态")
    private DeviceStatus status;
    
    @Schema(description = "所属机构ID")
    private Long organId;
    
    @Schema(description = "设备描述")
    private String description;
    
    @Getter
    public enum DeviceType implements TitleEnum {
        COLLECT_DEVICE("采集设备"),
        ANALYZER("分析器"),
        GENERATOR("生成器");
        
        private final String title;
        
        DeviceType(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
    
    @Getter
    public enum DeviceStatus implements TitleEnum {
        ONLINE("在线"),
        OFFLINE("离线"),
        MAINTENANCE("维护中"),
        FAULT("故障");
        
        private final String title;
        
        DeviceStatus(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
}
