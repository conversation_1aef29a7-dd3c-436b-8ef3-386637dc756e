package com.dcai.detect.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.dcai.detect.domain.item.Item")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "检测项")
public class ItemDTO extends BaseDTO<ItemDTO> {
    
    @Schema(description = "检测项名称")
    private String name;
    
    @Schema(description = "检测项代码")
    private String code;
    
    @Schema(description = "检测项描述")
    private String description;
    
    @Schema(description = "数据类型")
    private DataType dataType;
    
    @Schema(description = "单位")
    private String unit;
    
    @Schema(description = "标准值范围")
    private String standardRange;
    
    @Schema(description = "检测方法")
    private String method;

    @Schema(description = "数据生成方式")
    private GenerationMethod generationMethod;

    @Schema(description = "检测项状态")
    private ItemStatus status;
    
    @Getter
    public enum DataType implements TitleEnum {
        TEXT("文本"),
        NUMBER("数字"),
        DECIMAL("小数"),
        BOOLEAN("布尔值"),
        DATE("日期"),
        DATETIME("日期时间"),
        SELECT("单选"),
        MULTI_SELECT("多选"),
        FILE("文件");
        
        private final String title;
        
        DataType(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
    
    @Getter
    public enum ItemStatus implements TitleEnum {
        ACTIVE("启用"),
        INACTIVE("停用");
        
        private final String title;
        
        ItemStatus(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
}
