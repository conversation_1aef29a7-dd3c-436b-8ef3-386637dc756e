package com.dcai.detect.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.dcai.detect.domain.propertyplace.PropertyPlace")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "物业场所")
public class PropertyPlaceDTO extends BaseDTO<PropertyPlaceDTO> {
    
    @Schema(description = "场所名称")
    private String name;
    
    @Schema(description = "场所代码")
    private String code;
    
    @Schema(description = "场所类型")
    private PlaceType type;
    
    @Schema(description = "所属物业公司ID")
    private Long propertyCompanyId;
    
    @Schema(description = "详细地址")
    private String address;
    
    @Schema(description = "建筑面积(平方米)")
    private BigDecimal buildingArea;
    
    @Schema(description = "使用面积(平方米)")
    private BigDecimal usableArea;
    
    @Schema(description = "建成年份")
    private Integer buildYear;
    
    @Schema(description = "楼层数")
    private Integer floors;
    
    @Schema(description = "联系人")
    private String contactPerson;
    
    @Schema(description = "联系电话")
    private String contactPhone;
    
    @Schema(description = "场所状态")
    private PlaceStatus status;
    
    @Schema(description = "备注")
    private String remark;
    
    // 关联对象信息
    @Schema(description = "物业公司信息")
    private OrganDTO propertyCompany;
    
    @Getter
    public enum PlaceType implements TitleEnum {
        RESIDENTIAL("住宅小区"),
        OFFICE_BUILDING("写字楼"),
        SHOPPING_MALL("商场"),
        INDUSTRIAL("工业园区"),
        SCHOOL("学校"),
        HOSPITAL("医院"),
        HOTEL("酒店"),
        OTHER("其他");
        
        private final String title;
        
        PlaceType(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
    
    @Getter
    public enum PlaceStatus implements TitleEnum {
        ACTIVE("正常"),
        INACTIVE("停用"),
        UNDER_CONSTRUCTION("建设中"),
        DEMOLISHED("已拆除");
        
        private final String title;
        
        PlaceStatus(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
}
