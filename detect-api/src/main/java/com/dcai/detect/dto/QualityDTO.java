package com.dcai.detect.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.dcai.detect.domain.quality.Quality")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "资质管理")
public class QualityDTO extends BaseDTO<QualityDTO> {
    
    @Schema(description = "资质名称")
    private String name;
    
    @Schema(description = "资质代码")
    private String code;
    
    @Schema(description = "资质描述")
    private String description;
    
    @Schema(description = "资质类型")
    private QualityType type;
    
    @Schema(description = "资质等级")
    private QualityLevel level;
    
    @Schema(description = "颁发机构")
    private String issuer;
    
    @Schema(description = "有效期开始")
    private LocalDate validFrom;
    
    @Schema(description = "有效期结束")
    private LocalDate validTo;
    
    @Schema(description = "资质状态")
    private QualityStatus status;
    
    @Schema(description = "备注")
    private String remark;
    
    @Getter
    public enum QualityType implements TitleEnum {
        WATER_QUALITY("水质检测资质"),
        AIR_QUALITY("空气质量检测资质"),
        NOISE_LEVEL("噪音检测资质"),
        STRUCTURAL("结构检测资质"),
        ELECTRICAL("电气检测资质"),
        FIRE_SAFETY("消防安全检测资质"),
        COMPREHENSIVE("综合检测资质");
        
        private final String title;
        
        QualityType(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
    
    @Getter
    public enum QualityLevel implements TitleEnum {
        LEVEL_A("甲级"),
        LEVEL_B("乙级"),
        LEVEL_C("丙级"),
        SPECIAL("特级");
        
        private final String title;
        
        QualityLevel(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
    
    @Getter
    public enum QualityStatus implements TitleEnum {
        VALID("有效"),
        EXPIRED("过期"),
        SUSPENDED("暂停"),
        REVOKED("撤销");
        
        private final String title;
        
        QualityStatus(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
}
