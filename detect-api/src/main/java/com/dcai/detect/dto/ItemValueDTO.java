package com.dcai.detect.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.dcai.detect.domain.itemvalue.ItemValue")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "项目值")
public class ItemValueDTO extends BaseDTO<ItemValueDTO> {
    
    @Schema(description = "所属项目ID")
    private Long projectId;
    
    @Schema(description = "检测项ID")
    private Long itemId;
    
    @Schema(description = "物业场所ID")
    private Long propertyPlaceId;
    
    @Schema(description = "检测值")
    private String value;
    
    @Schema(description = "检测结果")
    private DetectResult result;
    
    @Schema(description = "检测位置")
    private String location;
    
    @Schema(description = "检测时间")
    private LocalDateTime detectTime;
    
    @Schema(description = "检测人员")
    private String detector;
    
    @Schema(description = "检测设备")
    private String equipment;
    
    @Schema(description = "环境条件")
    private String environment;
    
    @Schema(description = "数据状态")
    private ValueStatus status;
    
    @Schema(description = "备注")
    private String remark;
    
    // 关联对象信息
    @Schema(description = "项目信息")
    private ProjectDTO project;
    
    @Schema(description = "检测项信息")
    private ItemDTO item;
    
    @Schema(description = "物业场所信息")
    private PropertyPlaceDTO propertyPlace;
    
    @Getter
    public enum DetectResult implements TitleEnum {
        QUALIFIED("合格"),
        UNQUALIFIED("不合格"),
        PENDING("待判定"),
        EXCEPTION("异常");
        
        private final String title;
        
        DetectResult(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
    
    @Getter
    public enum ValueStatus implements TitleEnum {
        DRAFT("草稿"),
        CONFIRMED("已确认"),
        REVIEWED("已审核"),
        INVALID("无效");
        
        private final String title;
        
        ValueStatus(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
}
