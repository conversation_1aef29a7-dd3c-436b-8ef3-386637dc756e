package com.dcai.detect.dto;

import com.ejuetc.commons.base.entity.BaseDTO;
import com.ejuetc.commons.base.entity.TitleEnum;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@QueryDomain("com.dcai.detect.domain.detectitem.DetectItem")
@ToString(callSuper = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Schema(name = "检测套餐项关联")
public class DetectItemDTO extends BaseDTO<DetectItemDTO> {
    
    @Schema(description = "检测套餐ID")
    private Long detectId;
    
    @Schema(description = "检测项ID")
    private Long itemId;
    
    @Schema(description = "是否必检")
    private Boolean required;
    
    @Schema(description = "检测顺序")
    private Integer sortOrder;
    
    @Schema(description = "单项价格")
    private BigDecimal itemPrice;
    
    @Schema(description = "预计检测时长(分钟)")
    private Integer estimatedMinutes;
    
    @Schema(description = "特殊要求")
    private String specialRequirement;
    
    @Schema(description = "关联状态")
    private DetectItemStatus status;
    
    @Schema(description = "备注")
    private String remark;
    
    // 关联对象信息
    @Schema(description = "检测套餐信息")
    private DetectDTO detect;
    
    @Schema(description = "检测项信息")
    private ItemDTO item;
    
    @Getter
    public enum DetectItemStatus implements TitleEnum {
        ACTIVE("启用"),
        INACTIVE("停用");
        
        private final String title;
        
        DetectItemStatus(String title) {
            this.title = title;
        }
        
        @Override
        public String toString() {
            return name() + "-" + title;
        }
    }
}
